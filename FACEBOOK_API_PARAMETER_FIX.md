# 🔧 Facebook API Parameter Fix

## ✅ **Issue Being Resolved**

**Error**: `Facebook API Error: Invalid parameter` (400 Bad Request) when creating ad sets

**Root Cause**: The Facebook Graph API is very specific about request format, parameter types, and minimum values. The generic "Invalid parameter" error suggests issues with:
1. Request format (JSON vs FormData)
2. Budget minimums
3. Targeting format
4. Missing required fields

**Solution**: Enhanced the Facebook API integration with proper request formatting, better error handling, and corrected parameter values.

## 🔧 **Technical Improvements**

### **1. Request Format Fix**

#### **Before (JSON Body):**
```typescript
// POST/PUT requests
const bodyData = {
  access_token: facebookStatus.accessToken,
  ...data
};

options.body = JSON.stringify(bodyData);
```

#### **After (FormData):**
```typescript
// POST/PUT requests - use FormData for Facebook API
const formData = new FormData();
formData.append('access_token', facebookStatus.accessToken);

// Add all data fields to FormData
Object.keys(data).forEach(key => {
  const value = data[key];
  if (value !== undefined && value !== null) {
    formData.append(key, typeof value === 'object' ? JSON.stringify(value) : value.toString());
  }
});

// Remove Content-Type header to let browser set it for FormData
delete options.headers['Content-Type'];
options.body = formData;
```

### **2. Budget Validation**

#### **Before (Potential Issue):**
```typescript
daily_budget: Math.round(options.budget * 100), // Could be < 100
```

#### **After (Fixed):**
```typescript
const dailyBudgetCents = Math.max(Math.round(options.budget * 100), 100); // Minimum $1.00
daily_budget: dailyBudgetCents,
```

### **3. Enhanced Error Handling**

#### **Before (Generic):**
```typescript
if (result.error) {
  throw new Error(`Facebook API Error: ${result.error.message}`);
}
```

#### **After (Detailed):**
```typescript
if (result.error) {
  console.error('Facebook API Error Details:', result.error);
  const errorMessage = result.error.message || 'Unknown error';
  const errorCode = result.error.code || 'Unknown code';
  const errorSubcode = result.error.error_subcode || '';
  throw new Error(`Facebook API Error (${errorCode}${errorSubcode ? '/' + errorSubcode : ''}): ${errorMessage}`);
}
```

### **4. Simplified Targeting**

#### **Consistent Targeting Format:**
```typescript
const targetingData = {
  geo_locations: {
    countries: ['US']  // Simple, reliable targeting
  },
  age_min: 25,
  age_max: 65
};
```

## 🎯 **Facebook API Best Practices**

### **Request Format:**
- ✅ **FormData**: Facebook API prefers FormData over JSON for POST requests
- ✅ **Access Token**: Include in FormData, not URL for POST requests
- ✅ **Content-Type**: Let browser set automatically for FormData

### **Budget Requirements:**
- ✅ **Minimum Budget**: $1.00 daily (100 cents)
- ✅ **Currency**: Always in cents for Facebook API
- ✅ **Validation**: Ensure budget meets minimums

### **Targeting Requirements:**
- ✅ **JSON String**: Targeting must be JSON stringified
- ✅ **Required Fields**: geo_locations is required
- ✅ **Valid Values**: Age ranges must be 13-65

## 🔍 **Common Facebook API Issues & Solutions**

### **1. Budget Too Low**
```
Error: Daily budget must be at least $1.00
Solution: Math.max(budget * 100, 100)
```

### **2. Invalid Targeting**
```
Error: Invalid targeting parameter
Solution: Use simple geo_locations with countries
```

### **3. Wrong Request Format**
```
Error: Invalid parameter
Solution: Use FormData instead of JSON body
```

### **4. Missing Access Token**
```
Error: Invalid OAuth access token
Solution: Include access_token in FormData
```

## 🚀 **Expected Facebook API Calls**

### **Campaign Creation:**
```
POST https://graph.facebook.com/v23.0/act_{account_id}/campaigns
Content-Type: multipart/form-data

access_token={token}
name=House Washing Spring Special - 12/24/2024
objective=OUTCOME_LEADS
status=PAUSED
daily_budget=5000
```

### **Ad Set Creation:**
```
POST https://graph.facebook.com/v23.0/act_{account_id}/adsets
Content-Type: multipart/form-data

access_token={token}
name=House Washing Spring Special - AdSet
campaign_id=*****************
daily_budget=5000
billing_event=IMPRESSIONS
optimization_goal=LEAD_GENERATION
status=PAUSED
targeting={"geo_locations":{"countries":["US"]},"age_min":25,"age_max":65}
```

## 🔧 **Debugging Improvements**

### **Enhanced Error Messages:**
Now you'll see detailed error information like:
```
Facebook API Error (100/1885): The daily budget is too low. 
The minimum daily budget is $1.00.
```

Instead of just:
```
Facebook API Error: Invalid parameter
```

### **Console Logging:**
- ✅ **Request Data**: See exactly what's being sent
- ✅ **Error Details**: Full Facebook error response
- ✅ **Parameter Values**: Verify budget, targeting, etc.

## 🎯 **Testing the Fix**

### **What to Check:**
1. **Console Output**: Look for detailed error messages
2. **Budget Values**: Ensure daily_budget >= 100
3. **Request Format**: FormData instead of JSON
4. **Facebook Response**: More specific error details

### **Expected Results:**
- ✅ **Successful Campaign Creation**: Campaign appears in Facebook Ads Manager
- ✅ **Detailed Errors**: If still failing, specific error codes and messages
- ✅ **Better Debugging**: Clear indication of what parameter is invalid

## 🎉 **Benefits of the Fix**

### **Reliability:**
- ✅ **Proper Format**: Uses Facebook's preferred request format
- ✅ **Budget Validation**: Ensures minimum requirements are met
- ✅ **Error Handling**: Provides actionable error messages

### **Debugging:**
- ✅ **Detailed Errors**: Know exactly what's wrong
- ✅ **Console Logging**: See request data and responses
- ✅ **Parameter Validation**: Catch issues before sending to Facebook

### **User Experience:**
- ✅ **Successful Campaigns**: Higher success rate for campaign creation
- ✅ **Clear Feedback**: Better error messages for users
- ✅ **Faster Resolution**: Easier to identify and fix issues

The enhanced Facebook API integration should now successfully create campaigns with proper error handling and debugging! 🎯
