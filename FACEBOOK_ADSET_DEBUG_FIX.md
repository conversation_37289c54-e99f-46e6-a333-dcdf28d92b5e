# 🔧 Facebook Ad Set Debug Fix

## ✅ **Issue Being Debugged**

**Error**: `Facebook API Error: Invalid parameter` when creating ad sets

**Root Cause**: Generic "Invalid parameter" error from Facebook API suggests an issue with the ad set request format, missing required fields, or incorrect parameter values.

**Solution**: Enhanced ad set creation with proper parameter formatting and debugging to identify the exact issue.

## 🔧 **Technical Improvements**

### **1. Enhanced Ad Set Data Structure**

#### **Before (Potentially Problematic):**
```typescript
const adSetData: FacebookAdSetData = {
  name: `${template.name} - AdSet`,
  campaign_id: campaign.id,
  daily_budget: Math.round(options.budget * 100),
  billing_event: 'IMPRESSIONS',
  optimization_goal: 'LEAD_GENERATION',
  status: 'PAUSED',
  targeting: {
    geo_locations: options.targeting.geo_locations || {
      countries: ['US']
    },
    age_min: options.targeting.age_min || 25,
    age_max: options.targeting.age_max || 65
  }
};
```

#### **After (Improved):**
```typescript
// Separate targeting data for clarity
const targetingData = {
  geo_locations: options.targeting.geo_locations || {
    countries: ['US']
  },
  age_min: options.targeting.age_min || 25,
  age_max: options.targeting.age_max || 65
};

// Clean ad set data structure
const adSetData = {
  name: `${template.name} - AdSet`,
  campaign_id: campaign.id,
  daily_budget: Math.round(options.budget * 100),
  billing_event: 'IMPRESSIONS',
  optimization_goal: 'LEAD_GENERATION',
  status: 'PAUSED',
  targeting: JSON.stringify(targetingData)  // ✅ Properly formatted
};
```

### **2. Added Debugging**

```typescript
console.log('📝 Creating ad set...');
console.log('Ad Set Data:', JSON.stringify(adSetData, null, 2));
const adSet = await this.makeGraphAPICall(
  `act_${adAccountId}/adsets`,
  'POST',
  adSetData
);
```

## 🎯 **Facebook Ad Set Requirements (v23.0)**

### **Required Fields:**
- ✅ **name**: Ad set name
- ✅ **campaign_id**: Parent campaign ID
- ✅ **daily_budget**: Budget in cents
- ✅ **billing_event**: How you're charged (IMPRESSIONS)
- ✅ **optimization_goal**: What to optimize for (LEAD_GENERATION)
- ✅ **status**: Ad set status (PAUSED)
- ✅ **targeting**: JSON string with targeting parameters

### **Targeting Structure:**
```json
{
  "geo_locations": {
    "countries": ["US"]
    // OR
    "custom_locations": [{
      "latitude": 40.7128,
      "longitude": -74.0060,
      "radius": 25,
      "distance_unit": "mile"
    }]
  },
  "age_min": 25,
  "age_max": 65
}
```

## 🔍 **Common Facebook API Ad Set Issues**

### **1. Budget Issues:**
- **Problem**: Budget too low (minimum $1.00 = 100 cents)
- **Solution**: Ensure `daily_budget >= 100`

### **2. Targeting Issues:**
- **Problem**: Invalid targeting parameters
- **Solution**: Use proper geo_locations format

### **3. Campaign Objective Mismatch:**
- **Problem**: Optimization goal doesn't match campaign objective
- **Solution**: Use `LEAD_GENERATION` optimization with `OUTCOME_LEADS` objective

### **4. Status Issues:**
- **Problem**: Invalid status values
- **Solution**: Use 'PAUSED', 'ACTIVE', or 'ARCHIVED'

## 🚀 **Debugging Process**

### **Step 1: Check Console Output**
When creating a campaign, check the browser console for:
```
📝 Creating ad set...
Ad Set Data: {
  "name": "House Washing Spring Special - AdSet",
  "campaign_id": "*****************",
  "daily_budget": 5000,
  "billing_event": "IMPRESSIONS",
  "optimization_goal": "LEAD_GENERATION",
  "status": "PAUSED",
  "targeting": "{\"geo_locations\":{\"countries\":[\"US\"]},\"age_min\":25,\"age_max\":65}"
}
```

### **Step 2: Verify Parameters**
- ✅ **Budget**: Should be >= 100 (cents)
- ✅ **Campaign ID**: Should be valid Facebook campaign ID
- ✅ **Targeting**: Should be valid JSON string
- ✅ **Optimization Goal**: Should match campaign objective

### **Step 3: Check Facebook Response**
If still failing, the Facebook API should provide more specific error details.

## 🎯 **Expected Facebook API Call**

### **Request:**
```
POST https://graph.facebook.com/v23.0/act_{account_id}/adsets
Content-Type: application/json

{
  "access_token": "{access_token}",
  "name": "House Washing Spring Special - AdSet",
  "campaign_id": "*****************",
  "daily_budget": 5000,
  "billing_event": "IMPRESSIONS",
  "optimization_goal": "LEAD_GENERATION",
  "status": "PAUSED",
  "targeting": "{\"geo_locations\":{\"countries\":[\"US\"]},\"age_min\":25,\"age_max\":65}"
}
```

### **Expected Response:**
```json
{
  "id": "*****************",
  "success": true
}
```

## 🔧 **Troubleshooting Steps**

### **If Still Getting "Invalid Parameter":**

1. **Check Budget**: Ensure daily_budget >= 100 cents ($1.00)
2. **Verify Campaign**: Ensure campaign was created successfully
3. **Check Targeting**: Verify targeting JSON is valid
4. **Test Minimal**: Try with minimal targeting (just countries)
5. **Check Permissions**: Ensure access token has ads_management permission

### **Minimal Test Configuration:**
```typescript
const minimalAdSetData = {
  name: "Test AdSet",
  campaign_id: campaign.id,
  daily_budget: 100, // $1.00 minimum
  billing_event: "IMPRESSIONS",
  optimization_goal: "LEAD_GENERATION",
  status: "PAUSED",
  targeting: JSON.stringify({
    geo_locations: {
      countries: ["US"]
    },
    age_min: 18,
    age_max: 65
  })
};
```

## 🎉 **Next Steps**

1. **Test Campaign Creation**: Try creating a campaign with the improved ad set structure
2. **Check Console Logs**: Review the detailed ad set data being sent
3. **Verify Response**: Check if Facebook provides more specific error details
4. **Iterate**: Adjust parameters based on Facebook's response

The enhanced debugging will help identify the exact parameter causing the issue, allowing for a targeted fix! 🎯
