# 🚀 Facebook Marketing API Integration Guide

This guide will help you set up and use the Facebook Marketing API integration with PressureMax.

## ✅ **Fixed Issues**

- ✅ **Process Environment Error**: Fixed `process is not defined` error in React
- ✅ **API Configuration**: Hardcoded reliable API URL for browser compatibility
- ✅ **Loading States**: Added loading indicators for campaign launches
- ✅ **Error Handling**: Improved error messages and user feedback

## 🎯 **Quick Start**

### Option 1: Automated Setup (Recommended)

**Windows:**
```bash
start-facebook-integration.bat
```

**Mac/Linux:**
```bash
./start-facebook-integration.sh
```

### Option 2: Manual Setup

1. **Install Backend Dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env file with your Facebook app secret
   ```

3. **Start Backend Server**
   ```bash
   python app.py
   ```

4. **Start Frontend** (in new terminal)
   ```bash
   npm start
   ```

## 🔧 **Configuration**

### Facebook App Settings
- **App ID**: `394349039883481` ✅ (Already configured)
- **Access Token**: ✅ (Already configured)
- **Permissions**: `ads_management`, `ads_read`, `read_insights` ✅

### Required Files
- `backend/.env` - Facebook app secret and configuration
- `.env.local` - React environment variables (optional)

## 🎯 **How to Launch Campaigns**

### Step 1: Start the Backend
Make sure the Flask server is running on `http://localhost:5000`

### Step 2: Select a Template
1. Go to the PressureMax dashboard
2. Navigate to the "Templates" section
3. Browse the 20+ available campaign templates

### Step 3: Launch on Facebook
1. Click **"Launch on Facebook"** on any template
2. System automatically:
   - Checks Facebook API connection
   - Gets your ad accounts
   - Creates campaign structure
   - Uses template content for ads
   - Sets up targeting and budget

### Step 4: Review and Activate
1. Campaign is created in **paused** state
2. Review in Facebook Ads Manager
3. Activate when ready to start spending

## 📊 **Campaign Structure**

Each launch creates:
- **Campaign** - Lead generation objective
- **Ad Set** - Targeting and budget ($50/day default)
- **Creative** - Ad copy from template
- **Ad** - Final ad unit

## 🎯 **Targeting Options**

### Automatic Targeting
- **Location**: 25-mile radius (customizable)
- **Demographics**: Ages 25-65, all genders
- **Interests**: Home improvement, business (based on template)
- **Behaviors**: Homeowners or business decision makers

### Template-Specific Targeting
- **Residential Templates**: Homeowner targeting
- **Commercial Templates**: Business owner targeting
- **Seasonal Templates**: Relevant seasonal interests

## 💰 **Budget Management**

### Default Settings
- **Daily Budget**: $50/day per campaign
- **Billing**: Impressions-based
- **Optimization**: Lead generation
- **Status**: Paused (for review)

### Customization
- Modify budgets in Facebook Ads Manager
- Set campaign end dates
- Adjust targeting parameters
- A/B test different creatives

## 📈 **Performance Tracking**

### Available Metrics
- **Impressions**: Ad views
- **Clicks**: Ad clicks
- **Spend**: Amount spent
- **CPM**: Cost per 1,000 impressions
- **CPC**: Cost per click
- **CTR**: Click-through rate
- **Leads**: Lead generation actions
- **Cost per Lead**: Average lead cost

### Accessing Data
- Real-time data via API endpoints
- Facebook Ads Manager dashboard
- Future: Built-in PressureMax analytics

## 🛠️ **Troubleshooting**

### Common Issues

#### "Facebook API is not available"
**Solution**: Start the backend server
```bash
cd backend
python app.py
```

#### "No Facebook ad accounts found"
**Solutions**:
- Ensure you have a Facebook ad account
- Check Business Manager permissions
- Verify account is active

#### "Failed to create campaign"
**Solutions**:
- Check campaign name doesn't already exist
- Verify targeting parameters
- Ensure sufficient account permissions

#### Loading button stuck
**Solution**: Refresh the page and try again

### Debug Mode
Enable debug mode in `backend/facebook_api.py`:
```python
FacebookAdsApi.init(app_id, app_secret, access_token, debug=True)
```

## 🔐 **Security Notes**

### Access Token Management
- Tokens expire periodically
- Refresh tokens in Facebook Developer Console
- Monitor token status regularly

### App Secret Security
- Never commit app secrets to version control
- Store in environment variables only
- Use different secrets for development/production

## 🚀 **Advanced Features**

### Custom Targeting (Future)
- Upload custom audiences
- Lookalike audience creation
- Retargeting campaigns

### Automation (Future)
- Automatic budget optimization
- Performance-based scaling
- A/B testing automation

### Multi-Account Support (Future)
- Manage multiple ad accounts
- Agency-level features
- Client campaign separation

## 📞 **Support**

### Facebook Resources
- [Marketing API Docs](https://developers.facebook.com/docs/marketing-api)
- [Business SDK GitHub](https://github.com/facebook/facebook-python-business-sdk)
- [Facebook Developer Support](https://developers.facebook.com/support)

### PressureMax Support
- Check console logs for detailed errors
- Review backend logs for API issues
- Test with simple campaigns first

## 🎉 **Success Checklist**

- ✅ Backend server running on port 5000
- ✅ Frontend can connect to backend
- ✅ Facebook API health check passes
- ✅ Ad accounts are accessible
- ✅ Campaign launches successfully
- ✅ Campaign appears in Facebook Ads Manager
- ✅ Campaign can be activated and starts spending

## 🔮 **What's Next**

The Facebook integration is now fully functional! Users can:

1. **Launch Campaigns**: One-click campaign creation from templates
2. **Professional Setup**: Complete campaign structure automatically created
3. **Smart Targeting**: Audience targeting optimized for pressure washing
4. **Budget Control**: Safe defaults with easy customization
5. **Performance Tracking**: Real-time metrics and insights

The integration transforms PressureMax from a template library into a complete marketing automation platform for pressure washing businesses! 🎯
