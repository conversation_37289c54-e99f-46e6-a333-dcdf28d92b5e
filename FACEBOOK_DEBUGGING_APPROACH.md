# 🔍 Facebook API Debugging Approach

## 🎯 **Current Issue**

**Error**: `Facebook API Error (100/1815857): Invalid parameter` persisting despite previous fixes
**Status**: Still getting 400 Bad Request when creating ad sets
**Need**: More detailed error information and alternative approach

## 🔧 **Enhanced Debugging Strategy**

### **1. Improved Error Logging**

#### **Enhanced Error Details**:
```typescript
if (result.error) {
  console.error('Facebook API Error Details:', JSON.stringify(result.error, null, 2));
  console.error('Full Facebook Response:', JSON.stringify(result, null, 2));
  const errorMessage = result.error.message || 'Unknown error';
  const errorCode = result.error.code || 'Unknown code';
  const errorSubcode = result.error.error_subcode || '';
  const errorUserTitle = result.error.error_user_title || '';
  const errorUserMsg = result.error.error_user_msg || '';
  throw new Error(`Facebook API Error (${errorCode}${errorSubcode ? '/' + errorSubcode : ''}): ${errorMessage}${errorUserTitle ? ' - ' + errorUserTitle : ''}${errorUserMsg ? ' - ' + errorUserMsg : ''}`);
}
```

**Benefits**:
- ✅ **Full Error Details**: See complete Facebook error response
- ✅ **User Messages**: Facebook's user-friendly error explanations
- ✅ **Structured Data**: JSON format for easy analysis

### **2. Alternative Request Method**

#### **Changed from FormData to URL Parameters**:
```typescript
// Before: FormData approach
const formData = new FormData();
formData.append('access_token', token);
// ... add fields to FormData

// After: URL parameters approach
const urlWithParams = new URL(url);
urlWithParams.searchParams.append('access_token', token);
Object.keys(data).forEach(key => {
  const value = data[key];
  if (value !== undefined && value !== null) {
    if (typeof value === 'object') {
      urlWithParams.searchParams.append(key, JSON.stringify(value));
    } else {
      urlWithParams.searchParams.append(key, value.toString());
    }
  }
});
```

**Rationale**:
- ✅ **Simpler Format**: URL parameters are more straightforward
- ✅ **Better Debugging**: Can see exact URL being called
- ✅ **Facebook Compatibility**: Some Facebook endpoints prefer this format

### **3. Simplified Ad Set Configuration**

#### **Minimal Ad Set Approach**:
```typescript
// Before: Complex configuration
{
  name: `${template.name} - AdSet`,
  campaign_id: campaign.id,
  daily_budget: 500, // $5.00
  billing_event: 'IMPRESSIONS',
  optimization_goal: 'LEAD_GENERATION',
  status: 'PAUSED',
  targeting: {
    geo_locations: { countries: ['US'] },
    age_min: 25,
    age_max: 65
  }
}

// After: Minimal configuration
{
  name: `${template.name} AdSet`,
  campaign_id: campaign.id,
  daily_budget: 1000, // $10.00
  billing_event: 'IMPRESSIONS',
  optimization_goal: 'IMPRESSIONS', // Simpler optimization
  status: 'PAUSED',
  targeting: {
    geo_locations: { countries: ['US'] }
    // Removed age targeting to simplify
  }
}
```

**Changes Made**:
- ✅ **Higher Budget**: $10.00 minimum (eliminates budget issues)
- ✅ **Simpler Optimization**: IMPRESSIONS instead of LEAD_GENERATION
- ✅ **Minimal Targeting**: Only country targeting
- ✅ **Cleaner Name**: Removed special characters

## 🔍 **Debugging Process**

### **Step 1: Enhanced Error Information**
When the next campaign creation attempt is made, we'll see:
```
Facebook API Error Details: {
  "message": "Invalid parameter",
  "type": "OAuthException",
  "code": 100,
  "error_subcode": 1815857,
  "error_user_title": "Budget Too Low",
  "error_user_msg": "The daily budget must be at least $15.00 for this optimization goal",
  "fbtrace_id": "..."
}
```

### **Step 2: URL Inspection**
The console will show the exact URL being called:
```
Facebook API URL: https://graph.facebook.com/v23.0/act_263173616383414/adsets?access_token=...&name=House%20Washing%20AdSet&campaign_id=123&daily_budget=1000&billing_event=IMPRESSIONS&optimization_goal=IMPRESSIONS&status=PAUSED&targeting=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%7D
```

### **Step 3: Parameter Validation**
We can verify each parameter is correctly formatted and within Facebook's requirements.

## 🎯 **Expected Outcomes**

### **Scenario 1: Success**
- ✅ **Ad Set Created**: Campaign appears in Facebook Ads Manager
- ✅ **Simplified Config**: Proves minimal approach works
- ✅ **Foundation**: Can build complexity back up gradually

### **Scenario 2: Still Failing**
- ✅ **Detailed Error**: See exact Facebook error message
- ✅ **Specific Issue**: Identify the problematic parameter
- ✅ **Targeted Fix**: Address the specific issue Facebook reports

## 🔧 **Troubleshooting Matrix**

### **If Budget Error**:
- **Try**: Increase to $15.00, $20.00, or $25.00 daily
- **Reason**: Some optimization goals have higher minimums

### **If Targeting Error**:
- **Try**: Remove all targeting except countries
- **Reason**: Simplify to most basic targeting

### **If Campaign ID Error**:
- **Try**: Verify campaign was created successfully
- **Reason**: Ad set needs valid parent campaign

### **If Optimization Error**:
- **Try**: Use 'REACH' or 'IMPRESSIONS' optimization
- **Reason**: Simpler goals have fewer requirements

## 🚀 **Next Steps Strategy**

### **Phase 1: Get Basic Ad Set Working**
1. **Test with IMPRESSIONS optimization**: Simplest goal
2. **Use $10+ budget**: Eliminate budget issues
3. **Minimal targeting**: Just country targeting

### **Phase 2: Add Complexity Gradually**
1. **Add age targeting**: Once basic works
2. **Switch to LEAD_GENERATION**: Once targeting works
3. **Add custom locations**: Once lead generation works

### **Phase 3: Full Feature Implementation**
1. **Custom targeting options**: User-specified locations
2. **Dynamic budgets**: User-specified amounts
3. **Advanced optimization**: Quality leads, etc.

## 🎉 **Expected Resolution**

With enhanced debugging and simplified approach:

- ✅ **Clear Error Messages**: Know exactly what Facebook wants
- ✅ **Working Foundation**: Get basic ad set creation working
- ✅ **Incremental Improvement**: Build complexity step by step
- ✅ **Reliable Creation**: Consistent campaign creation success

The enhanced debugging will reveal the exact issue, allowing for a targeted fix! 🎯
