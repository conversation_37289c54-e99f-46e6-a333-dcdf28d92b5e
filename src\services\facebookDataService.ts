/**
 * Comprehensive Facebook Data Service for PressureMax
 * Handles fetching and processing all Facebook campaign data with proper filtering
 */

import { facebookIntegration } from './facebookIntegration';
import { dataCache, CacheKeys } from './dataCache';

export interface FacebookCampaignMetrics {
  impressions: number;
  clicks: number;
  spend: number;
  ctr: number;
  cpc: number;
  cpl: number;
  leads: number;
  reach: number;
  frequency: number;
  cpm: number; // Cost per mille (cost per 1000 impressions)
}

export interface FacebookAdCreative {
  id: string;
  name: string;
  title: string;
  body: string;
  call_to_action_type: string;
  image_url?: string;
  video_id?: string;
  thumbnail_url?: string;
  link_url?: string;
  object_story_spec?: any;
  asset_feed_spec?: any;
  image_hash?: string;
  image_crops?: any;
  video_data?: {
    video_id: string;
    image_url: string;
    video_url: string;
  };
  carousel_assets?: Array<{
    type: 'image' | 'video';
    id: string;
    url: string;
    width?: number;
    height?: number;
    thumbnail_url?: string;
    length?: number;
  }>;
}

export interface FacebookAdSet {
  id: string;
  name: string;
  campaign_id: string;
  status: string;
  daily_budget?: number;
  lifetime_budget?: number;
  bid_amount?: number;
  bid_strategy: string;
  optimization_goal: string;
  billing_event: string;
  targeting: {
    geo_locations?: any;
    age_min?: number;
    age_max?: number;
    genders?: number[];
    interests?: any[];
    behaviors?: any[];
    custom_audiences?: any[];
    lookalike_audiences?: any[];
    excluded_custom_audiences?: any[];
  };
  created_time: string;
  updated_time: string;
  start_time?: string;
  end_time?: string;
  metrics?: FacebookCampaignMetrics;
}

export interface FacebookAd {
  id: string;
  name: string;
  adset_id: string;
  campaign_id: string;
  status: string;
  creative: FacebookAdCreative;
  created_time: string;
  updated_time: string;
  metrics?: FacebookCampaignMetrics;
}

export interface FacebookCampaignData {
  id: string;
  name: string;
  objective: string;
  status: string;
  daily_budget?: number;
  lifetime_budget?: number;
  created_time: string;
  updated_time: string;
  start_time?: string;
  end_time?: string;
  special_ad_categories: string[];
  metrics: FacebookCampaignMetrics;
  adSets: FacebookAdSet[];
  ads: FacebookAd[];
  creatives: FacebookAdCreative[];
  account_id: string;
  account_name: string;
}

export interface FacebookLeadFormField {
  name: string;
  values: string[];
}

export interface FacebookLeadForm {
  id: string;
  name: string;
  status: string;
  locale: string;
  questions: Array<{
    key: string;
    label: string;
    type: string;
    required: boolean;
  }>;
  privacy_policy_url?: string;
  thank_you_page?: {
    title: string;
    body: string;
  };
}

export interface FacebookLead {
  id: string;
  created_time: string;
  ad_id: string;
  adset_id: string;
  campaign_id: string;
  form_id: string;
  field_data: FacebookLeadFormField[];
  is_organic: boolean;
  platform: string;
  // Enhanced lead data
  ad_name?: string;
  adset_name?: string;
  campaign_name?: string;
  form_name?: string;
  lead_quality_score?: number;
  contact_info: {
    email?: string;
    phone?: string;
    full_name?: string;
    first_name?: string;
    last_name?: string;
    company?: string;
    job_title?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    country?: string;
  };
  custom_fields: Record<string, string>;
  attribution: {
    campaign_name: string;
    adset_name: string;
    ad_name: string;
    creative_name: string;
  };
}

class FacebookDataService {
  private readonly API_VERSION = 'v23.0';
  private readonly BASE_URL = 'https://graph.facebook.com';
  private lastRequestTime = 0;
  private readonly MIN_REQUEST_INTERVAL = 500; // 500ms between requests

  /**
   * Rate limiting helper
   */
  private async waitForRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
      const waitTime = this.MIN_REQUEST_INTERVAL - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
  }

  /**
   * Make Facebook Graph API call with rate limiting
   */
  private async makeGraphAPICall(endpoint: string, params: any = {}): Promise<any> {
    await this.waitForRateLimit();
    
    const facebookStatus = facebookIntegration.getIntegrationStatus();
    
    if (!facebookStatus.isConnected || !facebookStatus.accessToken) {
      throw new Error('Facebook account not connected');
    }

    const url = new URL(`${this.BASE_URL}/${this.API_VERSION}/${endpoint}`);
    url.searchParams.append('access_token', facebookStatus.accessToken);
    
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key].toString());
      }
    });

    console.log(`🌐 Facebook API Call: ${endpoint}`, params);

    const response = await fetch(url.toString());
    const result = await response.json();

    if (result.error) {
      console.error(`❌ Facebook API Error for ${endpoint}:`, result.error);
      if (result.error.code === 4 || result.error.message.includes('rate limit')) {
        console.warn('⚠️ Facebook API rate limit reached');
        throw new Error('Rate limit reached');
      }
      throw new Error(`Facebook API Error: ${result.error.message}`);
    }

    console.log(`✅ Facebook API Response for ${endpoint}:`, result);
    return result;
  }

  /**
   * Get connected ad accounts (filtered by user's connected pages)
   */
  async getConnectedAdAccounts(): Promise<any[]> {
    const facebookStatus = facebookIntegration.getIntegrationStatus();

    if (!facebookStatus.isConnected) {
      throw new Error('Facebook not connected');
    }

    // Try to get from cache first
    return await dataCache.getOrSet(
      CacheKeys.FACEBOOK_AD_ACCOUNTS,
      async () => {
        console.log('🔍 Fetching ad accounts from Facebook API...');

        // Get all ad accounts
        const allAdAccounts = await this.makeGraphAPICall('/me/adaccounts', {
          fields: 'id,account_id,name,account_status,currency,timezone_name,amount_spent,balance'
        });

        console.log('✅ Successfully fetched ad accounts from Facebook API');

        // Get connected page IDs for filtering (if any pages are connected)
        const connectedPageIds = facebookStatus.pages.map(page => page.id);

        console.log(`📊 Found ${allAdAccounts.data?.length || 0} total ad accounts`);
        console.log(`📄 Found ${connectedPageIds.length} connected pages`);

    // If pages are connected, filter ad accounts based on page association
    // If no pages are connected, include all ad accounts (many advertisers don't use pages)
    if (connectedPageIds.length > 0) {
      console.log('🔍 Filtering ad accounts based on connected pages...');

      const filteredAccounts = [];

      for (const account of allAdAccounts.data || []) {
        try {
          // Check if this ad account is associated with any of our connected pages
          const accountPages = await this.makeGraphAPICall(`${account.id}/pages`, {
            fields: 'id,name'
          });

          const hasConnectedPage = accountPages.data?.some((page: any) =>
            connectedPageIds.includes(page.id)
          );

          if (hasConnectedPage) {
            filteredAccounts.push(account);
            console.log(`✅ Including ad account ${account.name} - connected to user's pages`);
          } else {
            console.log(`❌ Excluding ad account ${account.name} - not connected to user's pages`);
          }
        } catch (error) {
          console.warn(`⚠️ Could not check pages for account ${account.name}:`, error);
          // Include account if we can't verify the connection (benefit of the doubt)
          filteredAccounts.push(account);
          console.log(`✅ Including ad account ${account.name} - unable to verify page connection, including anyway`);
        }
      }

      console.log(`📊 Filtered to ${filteredAccounts.length} connected ad accounts from ${allAdAccounts.data?.length || 0} total accounts`);
      return filteredAccounts;
    } else {
      // No pages connected - include all ad accounts
      console.log('📝 No pages connected - including all ad accounts (many advertisers don\'t use pages)');
      console.log(`📊 Including all ${allAdAccounts.data?.length || 0} ad accounts`);
      return allAdAccounts.data || [];
    }
      },
      5 * 60 * 1000 // Cache for 5 minutes
    );
  }

  /**
   * Get campaign metrics with insights
   */
  private async getCampaignMetrics(campaignId: string): Promise<FacebookCampaignMetrics> {
    try {
      const insights = await this.makeGraphAPICall(`${campaignId}/insights`, {
        fields: 'impressions,clicks,spend,ctr,cpc,reach,frequency,cpm,actions',
        time_range: JSON.stringify({
          since: '2024-01-01',
          until: new Date().toISOString().split('T')[0]
        })
      });

      const data = insights.data?.[0] || {};
      
      // Extract lead generation actions
      const actions = data.actions || [];
      const leadAction = actions.find((action: any) => action.action_type === 'lead');
      const leads = leadAction ? parseInt(leadAction.value) : 0;
      
      return {
        impressions: parseInt(data.impressions) || 0,
        clicks: parseInt(data.clicks) || 0,
        spend: parseFloat(data.spend) || 0,
        ctr: parseFloat(data.ctr) || 0,
        cpc: parseFloat(data.cpc) || 0,
        cpl: leads > 0 ? (parseFloat(data.spend) || 0) / leads : 0,
        leads: leads,
        reach: parseInt(data.reach) || 0,
        frequency: parseFloat(data.frequency) || 0,
        cpm: parseFloat(data.cpm) || 0
      };
    } catch (error) {
      console.warn(`Could not fetch metrics for campaign ${campaignId}:`, error);
      return {
        impressions: 0,
        clicks: 0,
        spend: 0,
        ctr: 0,
        cpc: 0,
        cpl: 0,
        leads: 0,
        reach: 0,
        frequency: 0,
        cpm: 0
      };
    }
  }

  /**
   * Get ad creative details
   */
  private async getAdCreative(creativeId: string): Promise<FacebookAdCreative> {
    try {
      const creative = await this.makeGraphAPICall(creativeId, {
        fields: 'id,name,title,body,call_to_action_type,image_url,video_id,thumbnail_url,object_story_spec'
      });

      return {
        id: creative.id,
        name: creative.name || '',
        title: creative.title || creative.object_story_spec?.page_post_spec?.message || '',
        body: creative.body || creative.object_story_spec?.page_post_spec?.description || '',
        call_to_action_type: creative.call_to_action_type || 'LEARN_MORE',
        image_url: creative.image_url,
        video_id: creative.video_id,
        thumbnail_url: creative.thumbnail_url,
        link_url: creative.object_story_spec?.link_data?.link || '',
        object_story_spec: creative.object_story_spec
      };
    } catch (error) {
      console.warn(`Could not fetch creative ${creativeId}:`, error);
      return {
        id: creativeId,
        name: 'Unknown Creative',
        title: '',
        body: '',
        call_to_action_type: 'LEARN_MORE'
      };
    }
  }

  /**
   * Get ad set metrics with insights
   */
  private async getAdSetMetrics(adSetId: string): Promise<FacebookCampaignMetrics> {
    return this.getCampaignMetrics(adSetId); // Same API structure for insights
  }

  /**
   * Get ad metrics with insights
   */
  private async getAdMetrics(adId: string): Promise<FacebookCampaignMetrics> {
    return this.getCampaignMetrics(adId); // Same API structure for insights
  }

  /**
   * Get enhanced ad creative details with media assets
   */
  private async getAdCreativeDetails(creativeId: string): Promise<FacebookAdCreative> {
    try {
      const creative = await this.makeGraphAPICall(creativeId, {
        fields: 'id,name,title,body,call_to_action_type,image_url,image_hash,image_crops,video_id,thumbnail_url,link_url,object_story_spec,asset_feed_spec'
      });

      // Get video data if video_id exists
      let video_data;
      if (creative.video_id) {
        try {
          const video = await this.makeGraphAPICall(creative.video_id, {
            fields: 'id,source,picture'
          });
          video_data = {
            video_id: creative.video_id,
            image_url: video.picture || creative.thumbnail_url,
            video_url: video.source
          };
        } catch (error) {
          console.warn(`Could not fetch video data for ${creative.video_id}:`, error);
        }
      }

      // Process carousel assets if present
      let carousel_assets;
      if (creative.asset_feed_spec) {
        carousel_assets = await this.processCarouselAssets(creative.asset_feed_spec);
      }

      return {
        id: creative.id,
        name: creative.name || 'Untitled Creative',
        title: creative.title || creative.object_story_spec?.page_post_spec?.message || '',
        body: creative.body || creative.object_story_spec?.page_post_spec?.description || '',
        call_to_action_type: creative.call_to_action_type || 'LEARN_MORE',
        image_url: creative.image_url,
        video_id: creative.video_id,
        thumbnail_url: creative.thumbnail_url,
        link_url: creative.link_url || creative.object_story_spec?.link_data?.link || '',
        object_story_spec: creative.object_story_spec,
        asset_feed_spec: creative.asset_feed_spec,
        image_hash: creative.image_hash,
        image_crops: creative.image_crops,
        video_data,
        carousel_assets
      };
    } catch (error) {
      console.warn(`Could not fetch enhanced creative details for ${creativeId}:`, error);
      // Fallback to basic creative method
      return this.getAdCreative(creativeId);
    }
  }

  /**
   * Process carousel assets from asset feed spec
   */
  private async processCarouselAssets(assetFeedSpec: any): Promise<any[]> {
    const assets = [];

    try {
      // Process images
      if (assetFeedSpec.images) {
        for (const imageSpec of assetFeedSpec.images) {
          if (imageSpec.hash) {
            try {
              const imageData = await this.makeGraphAPICall(imageSpec.hash, {
                fields: 'id,url,width,height'
              });
              assets.push({
                type: 'image',
                id: imageData.id,
                url: imageData.url,
                width: imageData.width,
                height: imageData.height
              });
            } catch (error) {
              console.warn(`Could not fetch image data for hash ${imageSpec.hash}:`, error);
            }
          }
        }
      }

      // Process videos
      if (assetFeedSpec.videos) {
        for (const videoSpec of assetFeedSpec.videos) {
          if (videoSpec.video_id) {
            try {
              const videoData = await this.makeGraphAPICall(videoSpec.video_id, {
                fields: 'id,source,picture,length'
              });
              assets.push({
                type: 'video',
                id: videoData.id,
                url: videoData.source,
                thumbnail_url: videoData.picture,
                length: videoData.length
              });
            } catch (error) {
              console.warn(`Could not fetch video data for ${videoSpec.video_id}:`, error);
            }
          }
        }
      }
    } catch (error) {
      console.warn('Error processing carousel assets:', error);
    }

    return assets;
  }

  /**
   * Get leads from Facebook Lead Ads with enhanced details
   */
  async getLeadsFromCampaign(campaignId: string): Promise<FacebookLead[]> {
    try {
      const leads = await this.makeGraphAPICall(`${campaignId}/leads`, {
        fields: 'id,created_time,ad_id,adset_id,campaign_id,form_id,field_data,is_organic,platform'
      });

      const enhancedLeads: FacebookLead[] = [];

      for (const lead of leads.data || []) {
        try {
          const enhancedLead = await this.enhanceLeadWithDetails(lead);
          enhancedLeads.push(enhancedLead);
        } catch (error) {
          console.warn(`Could not enhance lead ${lead.id}:`, error);
          // Add basic lead data if enhancement fails
          enhancedLeads.push(this.createBasicLead(lead));
        }
      }

      return enhancedLeads;
    } catch (error) {
      console.warn(`Could not fetch leads for campaign ${campaignId}:`, error);
      return [];
    }
  }

  /**
   * Enhance lead with detailed attribution and contact information
   */
  private async enhanceLeadWithDetails(basicLead: any): Promise<FacebookLead> {
    // Get campaign, ad set, and ad details for attribution
    const [campaign, adSet, ad, form] = await Promise.all([
      this.getCampaignDetails(basicLead.campaign_id),
      this.getAdSetDetails(basicLead.adset_id),
      this.getAdDetails(basicLead.ad_id),
      this.getLeadFormDetails(basicLead.form_id)
    ]);

    // Parse contact information from field data
    const contactInfo = this.parseContactInfo(basicLead.field_data);
    const customFields = this.parseCustomFields(basicLead.field_data);

    // Calculate lead quality score based on completeness
    const leadQualityScore = this.calculateLeadQualityScore(contactInfo, customFields);

    return {
      id: basicLead.id,
      created_time: basicLead.created_time,
      ad_id: basicLead.ad_id,
      adset_id: basicLead.adset_id,
      campaign_id: basicLead.campaign_id,
      form_id: basicLead.form_id,
      field_data: basicLead.field_data,
      is_organic: basicLead.is_organic,
      platform: basicLead.platform,
      ad_name: ad?.name,
      adset_name: adSet?.name,
      campaign_name: campaign?.name,
      form_name: form?.name,
      lead_quality_score: leadQualityScore,
      contact_info: contactInfo,
      custom_fields: customFields,
      attribution: {
        campaign_name: campaign?.name || 'Unknown Campaign',
        adset_name: adSet?.name || 'Unknown Ad Set',
        ad_name: ad?.name || 'Unknown Ad',
        creative_name: ad?.creative?.name || 'Unknown Creative'
      }
    };
  }

  /**
   * Create basic lead structure when enhancement fails
   */
  private createBasicLead(basicLead: any): FacebookLead {
    const contactInfo = this.parseContactInfo(basicLead.field_data);
    const customFields = this.parseCustomFields(basicLead.field_data);

    return {
      id: basicLead.id,
      created_time: basicLead.created_time,
      ad_id: basicLead.ad_id,
      adset_id: basicLead.adset_id,
      campaign_id: basicLead.campaign_id,
      form_id: basicLead.form_id,
      field_data: basicLead.field_data,
      is_organic: basicLead.is_organic,
      platform: basicLead.platform,
      lead_quality_score: this.calculateLeadQualityScore(contactInfo, customFields),
      contact_info: contactInfo,
      custom_fields: customFields,
      attribution: {
        campaign_name: 'Unknown Campaign',
        adset_name: 'Unknown Ad Set',
        ad_name: 'Unknown Ad',
        creative_name: 'Unknown Creative'
      }
    };
  }

  /**
   * Parse contact information from lead field data
   */
  private parseContactInfo(fieldData: FacebookLeadFormField[]): FacebookLead['contact_info'] {
    const contactInfo: FacebookLead['contact_info'] = {};

    for (const field of fieldData) {
      const value = field.values?.[0] || '';

      switch (field.name.toLowerCase()) {
        case 'email':
          contactInfo.email = value;
          break;
        case 'phone_number':
        case 'phone':
          contactInfo.phone = value;
          break;
        case 'full_name':
          contactInfo.full_name = value;
          break;
        case 'first_name':
          contactInfo.first_name = value;
          break;
        case 'last_name':
          contactInfo.last_name = value;
          break;
        case 'company_name':
        case 'company':
          contactInfo.company = value;
          break;
        case 'job_title':
          contactInfo.job_title = value;
          break;
        case 'city':
          contactInfo.city = value;
          break;
        case 'state':
          contactInfo.state = value;
          break;
        case 'zip_code':
        case 'postal_code':
          contactInfo.zip_code = value;
          break;
        case 'country':
          contactInfo.country = value;
          break;
      }
    }

    // If full_name is not provided but first_name and last_name are, combine them
    if (!contactInfo.full_name && contactInfo.first_name && contactInfo.last_name) {
      contactInfo.full_name = `${contactInfo.first_name} ${contactInfo.last_name}`;
    }

    return contactInfo;
  }

  /**
   * Parse custom fields from lead field data
   */
  private parseCustomFields(fieldData: FacebookLeadFormField[]): Record<string, string> {
    const customFields: Record<string, string> = {};
    const standardFields = ['email', 'phone_number', 'phone', 'full_name', 'first_name', 'last_name',
                           'company_name', 'company', 'job_title', 'city', 'state', 'zip_code', 'postal_code', 'country'];

    for (const field of fieldData) {
      if (!standardFields.includes(field.name.toLowerCase())) {
        customFields[field.name] = field.values?.[0] || '';
      }
    }

    return customFields;
  }

  /**
   * Calculate lead quality score based on completeness and data quality
   */
  private calculateLeadQualityScore(contactInfo: FacebookLead['contact_info'], customFields: Record<string, string>): number {
    let score = 0;
    let maxScore = 0;

    // Email (30 points)
    maxScore += 30;
    if (contactInfo.email && this.isValidEmail(contactInfo.email)) {
      score += 30;
    } else if (contactInfo.email) {
      score += 15; // Partial points for invalid email
    }

    // Phone (25 points)
    maxScore += 25;
    if (contactInfo.phone && this.isValidPhone(contactInfo.phone)) {
      score += 25;
    } else if (contactInfo.phone) {
      score += 10; // Partial points for invalid phone
    }

    // Name (20 points)
    maxScore += 20;
    if (contactInfo.full_name || (contactInfo.first_name && contactInfo.last_name)) {
      score += 20;
    } else if (contactInfo.first_name || contactInfo.last_name) {
      score += 10;
    }

    // Company/Job Title (15 points)
    maxScore += 15;
    if (contactInfo.company && contactInfo.job_title) {
      score += 15;
    } else if (contactInfo.company || contactInfo.job_title) {
      score += 8;
    }

    // Location (10 points)
    maxScore += 10;
    const locationFields = [contactInfo.city, contactInfo.state, contactInfo.zip_code, contactInfo.country];
    const filledLocationFields = locationFields.filter(field => field && field.trim().length > 0);
    score += Math.min(10, filledLocationFields.length * 2.5);

    // Custom fields bonus (up to 10 points)
    const customFieldCount = Object.keys(customFields).length;
    score += Math.min(10, customFieldCount * 2);

    return Math.round((score / maxScore) * 100);
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone format
   */
  private isValidPhone(phone: string): boolean {
    // Remove all non-digit characters
    const digits = phone.replace(/\D/g, '');
    // Check if it's a reasonable phone number length (7-15 digits)
    return digits.length >= 7 && digits.length <= 15;
  }

  /**
   * Get campaign details for attribution
   */
  private async getCampaignDetails(campaignId: string): Promise<any> {
    try {
      return await this.makeGraphAPICall(campaignId, {
        fields: 'id,name'
      });
    } catch (error) {
      console.warn(`Could not fetch campaign details for ${campaignId}:`, error);
      return null;
    }
  }

  /**
   * Get ad set details for attribution
   */
  private async getAdSetDetails(adSetId: string): Promise<any> {
    try {
      return await this.makeGraphAPICall(adSetId, {
        fields: 'id,name'
      });
    } catch (error) {
      console.warn(`Could not fetch ad set details for ${adSetId}:`, error);
      return null;
    }
  }

  /**
   * Get ad details for attribution
   */
  private async getAdDetails(adId: string): Promise<any> {
    try {
      return await this.makeGraphAPICall(adId, {
        fields: 'id,name,creative'
      });
    } catch (error) {
      console.warn(`Could not fetch ad details for ${adId}:`, error);
      return null;
    }
  }

  /**
   * Get lead form details
   */
  private async getLeadFormDetails(formId: string): Promise<FacebookLeadForm | null> {
    try {
      const form = await this.makeGraphAPICall(formId, {
        fields: 'id,name,status,locale,questions,privacy_policy_url,thank_you_page'
      });

      return form;
    } catch (error) {
      console.warn(`Could not fetch lead form details for ${formId}:`, error);
      return null;
    }
  }

  /**
   * Get all leads from all connected campaigns
   */
  async getAllLeads(): Promise<FacebookLead[]> {
    return await dataCache.getOrSet(
      CacheKeys.FACEBOOK_LEADS,
      async () => {
        try {
          const campaigns = await this.getAllCampaignData();
          const allLeads: FacebookLead[] = [];

      for (const campaign of campaigns) {
        // Only get leads from lead generation campaigns
        if (campaign.objective === 'LEAD_GENERATION') {
          try {
            const campaignLeads = await this.getLeadsFromCampaign(campaign.id);
            allLeads.push(...campaignLeads);
          } catch (error) {
            console.warn(`Error fetching leads for campaign ${campaign.name}:`, error);
          }
        }
      }

          return allLeads;
        } catch (error) {
          console.error('Error fetching Facebook leads:', error);
          return [];
        }
      },
      10 * 60 * 1000 // Cache for 10 minutes
    );
  }

  /**
   * Force refresh all Facebook data (clears cache)
   */
  async forceRefreshData(): Promise<void> {
    console.log('🔄 Force refreshing Facebook data...');
    dataCache.delete(CacheKeys.FACEBOOK_CAMPAIGNS);
    dataCache.delete(CacheKeys.FACEBOOK_AD_ACCOUNTS);
    dataCache.delete(CacheKeys.FACEBOOK_LEADS);

    console.log('✅ Cache cleared, loading fresh data...');

    // Trigger fresh data load
    const campaigns = await this.getAllCampaignData();
    const leads = await this.getAllLeads();

    console.log(`🎯 Refresh complete: ${campaigns.length} campaigns, ${leads.length} leads`);
  }

  /**
   * Get comprehensive campaign data for connected accounts only
   */
  async getAllCampaignData(): Promise<FacebookCampaignData[]> {

    return await dataCache.getOrSet(
      CacheKeys.FACEBOOK_CAMPAIGNS,
      async () => {
        try {
          const connectedAccounts = await this.getConnectedAdAccounts();
          const allCampaigns: FacebookCampaignData[] = [];

          console.log(`📊 Fetching campaigns from ${connectedAccounts.length} connected ad accounts`);

          if (connectedAccounts.length === 0) {
            console.warn('⚠️ No connected ad accounts found - cannot fetch campaigns');
            return [];
          }

      for (const account of connectedAccounts) {
        try {
          console.log(`🔍 Processing account: ${account.name}`);
          
          // Get campaigns for this account
          const campaigns = await this.makeGraphAPICall(`${account.id}/campaigns`, {
            fields: 'id,name,objective,status,created_time,updated_time,daily_budget,lifetime_budget,special_ad_categories',
            limit: 50
          });

          console.log(`📈 Found ${campaigns.data?.length || 0} campaigns in account ${account.name}`);

          for (const campaign of campaigns.data || []) {
            try {
              // Get campaign metrics
              const metrics = await this.getCampaignMetrics(campaign.id);

              // Get ad sets for this campaign
              const adSets = await this.makeGraphAPICall(`${campaign.id}/adsets`, {
                fields: 'id,name,status,daily_budget,lifetime_budget,billing_event,optimization_goal,bid_strategy,bid_amount,targeting,created_time,updated_time',
                limit: 25
              });

              const processedAdSets: FacebookAdSet[] = [];
              const allCampaignAds: FacebookAd[] = [];

              for (const adSet of adSets.data || []) {
                try {
                  // Get ad set metrics
                  const adSetMetrics = await this.getAdSetMetrics(adSet.id);

                  // Get ads for this ad set
                  const ads = await this.makeGraphAPICall(`${adSet.id}/ads`, {
                    fields: 'id,name,status,creative,created_time,updated_time',
                    limit: 25
                  });

                  const processedAds: FacebookAd[] = [];

                  for (const ad of ads.data || []) {
                    try {
                      const adMetrics = await this.getAdMetrics(ad.id);
                      let creative: FacebookAdCreative;

                      if (ad.creative?.id) {
                        creative = await this.getAdCreativeDetails(ad.creative.id);
                      } else {
                        creative = {
                          id: 'unknown',
                          name: 'Unknown Creative',
                          title: '',
                          body: '',
                          call_to_action_type: 'LEARN_MORE'
                        };
                      }

                      const processedAd = {
                        id: ad.id,
                        name: ad.name,
                        adset_id: adSet.id,
                        campaign_id: campaign.id,
                        status: ad.status,
                        creative,
                        metrics: adMetrics,
                        created_time: ad.created_time,
                        updated_time: ad.updated_time
                      };

                      processedAds.push(processedAd);
                      allCampaignAds.push(processedAd);
                    } catch (error) {
                      console.warn(`Error processing ad ${ad.id}:`, error);
                    }
                  }

                  processedAdSets.push({
                    id: adSet.id,
                    name: adSet.name,
                    campaign_id: campaign.id,
                    status: adSet.status,
                    daily_budget: adSet.daily_budget,
                    lifetime_budget: adSet.lifetime_budget,
                    bid_strategy: adSet.bid_strategy,
                    bid_amount: adSet.bid_amount,
                    optimization_goal: adSet.optimization_goal,
                    billing_event: adSet.billing_event,
                    targeting: adSet.targeting || {},
                    metrics: adSetMetrics,
                    created_time: adSet.created_time,
                    updated_time: adSet.updated_time,
                    start_time: adSet.start_time,
                    end_time: adSet.end_time
                  });
                } catch (error) {
                  console.warn(`Error processing ad set ${adSet.id}:`, error);
                }
              }

              // Collect all creatives from all ads
              const allCreatives: FacebookAdCreative[] = allCampaignAds.map(ad => ad.creative);

              allCampaigns.push({
                id: campaign.id,
                name: campaign.name,
                objective: campaign.objective,
                status: campaign.status,
                daily_budget: campaign.daily_budget,
                lifetime_budget: campaign.lifetime_budget,
                created_time: campaign.created_time,
                updated_time: campaign.updated_time,
                start_time: campaign.start_time,
                end_time: campaign.end_time,
                special_ad_categories: campaign.special_ad_categories || [],
                metrics,
                adSets: processedAdSets,
                ads: allCampaignAds,
                creatives: allCreatives,
                account_id: account.account_id,
                account_name: account.name
              });

              console.log(`✅ Processed campaign: ${campaign.name}`);
            } catch (error) {
              console.warn(`Error processing campaign ${campaign.name}:`, error);
            }
          }
        } catch (error) {
          console.error(`Error processing account ${account.name}:`, error);
        }
      }

          console.log(`🎉 Successfully processed ${allCampaigns.length} campaigns`);
          return allCampaigns;
        } catch (error) {
          console.error('Error fetching Facebook campaign data:', error);
          throw error;
        }
      },
      15 * 60 * 1000 // Cache for 15 minutes
    );
  }
  /**
   * Clear all cached Facebook data
   */
  clearCache(): void {
    dataCache.delete(CacheKeys.FACEBOOK_CAMPAIGNS);
    dataCache.delete(CacheKeys.FACEBOOK_LEADS);
    dataCache.delete(CacheKeys.FACEBOOK_AD_ACCOUNTS);
    console.log('🧹 Cleared Facebook data cache');
  }

  /**
   * Refresh all Facebook data by clearing cache and fetching fresh data
   */
  async refreshAllData(): Promise<void> {
    console.log('🔄 Refreshing all Facebook data...');
    this.clearCache();

    // Trigger fresh data fetch
    await Promise.all([
      this.getAllCampaignData(),
      this.getAllLeads(),
      this.getConnectedAdAccounts()
    ]);

    console.log('✅ Facebook data refresh complete');
  }

  /**
   * Start automatic data refresh scheduling
   */
  startAutoRefresh(intervalMinutes: number = 30): void {
    // Clear any existing interval
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    this.refreshInterval = setInterval(async () => {
      try {
        console.log('⏰ Automatic Facebook data refresh triggered');
        await this.refreshAllData();
      } catch (error) {
        console.error('❌ Automatic refresh failed:', error);
      }
    }, intervalMinutes * 60 * 1000);

    console.log(`⏰ Started automatic Facebook data refresh every ${intervalMinutes} minutes`);
  }

  /**
   * Stop automatic data refresh
   */
  stopAutoRefresh(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
      console.log('⏹️ Stopped automatic Facebook data refresh');
    }
  }

  private refreshInterval: NodeJS.Timeout | null = null;
}

// Export singleton instance
export const facebookDataService = new FacebookDataService();

// Expose for debugging
(window as any).facebookDataService = facebookDataService;
