# VAPI Integration for PressureMax

## Overview

PressureMax now includes advanced AI voice calling capabilities powered by VAPI (Voice AI Platform) with **automatic new lead calling** and intelligent appointment booking through voice conversations.

## 🔥 NEW: Automatic Lead Calling System

**NEW LEADS ARE NOW CALLED AUTOMATICALLY!** When a new lead comes in, the system immediately triggers a VAPI call with our AI appointment booking assistant. No manual intervention required.

## Features

### ✅ Fixed Issues
- **SVG Campaign Images**: Fixed display of SVG placeholders for Post-Construction Cleanup, Window Cleaning Add-On, and Restaurant Exterior Cleaning templates
- **Image Paths**: Updated database to correctly reference SVG files for missing campaign images

### 🆕 New VAPI Integration Features

#### 1. 🔥 Automatic New Lead Calling (NEW!)
- **Instant Response**: New leads are called within seconds of creation
- **Smart Prioritization**: High-priority leads (hot/warm, urgent) get immediate calls
- **Business Hours Awareness**: Respects business hours (9 AM - 6 PM) for regular leads
- **Rate Limiting**: Built-in protection against system overload (20 calls/hour max)
- **Queue Management**: Intelligent queuing system for optimal call timing

#### 2. AI Appointment Booking Assistant
- **Smart Conversation Flow**: AI assistant follows a structured workflow to book appointments
- **Appointment Tools**: Integrated calendar checking and booking functionality
- **Lead Qualification**: Automatically qualifies leads during the call
- **Real-time Updates**: Lead status updates based on call outcomes

#### 3. Reactivation Campaigns (Bulk Calling)
- **Follow-up Calls**: Target previously contacted leads who didn't book appointments
- **Automated Outreach**: Call multiple leads with a single click
- **Rate Limiting**: 3-second delays between calls to prevent system overload
- **Progress Tracking**: Real-time progress indicator during campaigns

#### 3. Enhanced Lead Management
- **Call Status Tracking**: Monitor call progress and outcomes
- **Appointment Scheduling**: Direct integration with calendar system
- **Lead Quality Updates**: Automatic lead scoring based on call results
- **Detailed Notes**: Comprehensive call logs and transcripts

## Technical Implementation

### Core Components

1. **VAPIService** (`src/services/vapi.ts`)
   - Main VAPI integration service
   - Assistant creation and management
   - Call initiation and monitoring
   - Appointment booking tools

2. **VAPIApiService** (`src/services/vapiApi.ts`)
   - Webhook endpoint handlers
   - Appointment availability checking
   - Calendar integration
   - Lead data management

3. **VAPICallManager** (`src/components/VAPICallManager.tsx`)
   - Individual lead calling interface
   - Call progress monitoring
   - Real-time status updates

4. **Enhanced App.tsx**
   - Bulk calling functionality
   - Progress tracking UI
   - Lead management integration

### VAPI Assistant Configuration

The AI assistant is configured with:
- **GPT-4o Model**: Latest OpenAI model for optimal conversation quality
- **ElevenLabs Voice**: Professional female voice (Sarah)
- **Appointment Tools**: Calendar checking and booking functions
- **Smart Prompts**: Structured conversation flow for appointment booking

### Appointment Booking Flow

1. **Lead Qualification**: Verify interest and gather service details
2. **Availability Check**: Query calendar for open appointment slots
3. **Offer Options**: Present 2-3 available time slots to customer
4. **Confirm Booking**: Finalize appointment with customer details
5. **Update Records**: Automatically update lead status and calendar

## Usage Instructions

### 🔥 Automatic New Lead Calling (No Action Required!)

**The system now automatically calls new leads!** When a lead is created:

1. **Immediate Trigger**: System detects new lead creation
2. **Smart Assessment**: Evaluates lead priority and timing
3. **Automatic Call**: VAPI assistant calls the lead within seconds
4. **Status Updates**: Lead status automatically updated based on call outcome
5. **Appointment Booking**: AI can book appointments directly during the call

**Visual Indicator**: Look for the green "Auto-Call: ON" status in the leads section.

### Testing Automatic Calling

1. Navigate to the **Leads** section
2. Click the **"Test Auto-Call"** button
3. A test lead will be created and automatically called
4. Monitor the console and lead status for real-time updates

### Starting a Reactivation Campaign

1. Navigate to the **Leads** section
2. Click the **"Reactivation Campaign"** button
3. Confirm the campaign (shows number of leads to be called)
4. Monitor progress in real-time
5. Review results after completion

### Individual Lead Calling

1. Open any lead's details modal
2. Use the **VAPICallManager** component
3. Click **"Call Lead"** to initiate AI call
4. Monitor call progress and status
5. Review call outcomes and appointment bookings

### Monitoring Call Results

- **Lead Status Updates**: Automatic status changes based on call outcomes
- **Appointment Scheduling**: Direct calendar integration for booked appointments
- **Call Transcripts**: Detailed conversation logs (when available)
- **Quality Scoring**: Lead quality updates based on conversation sentiment

## Configuration

### Environment Variables

```env
REACT_APP_VAPI_API_KEY=your_vapi_api_key_here
VAPI_SERVER_PORT=3001
```

### VAPI Webhook Setup

The integration includes webhook endpoints for:
- `/api/vapi/check-availability` - Calendar availability checking
- `/api/vapi/book-appointment` - Appointment booking
- `/api/vapi/webhook` - Call event handling

## Benefits

### For Business Owners
- **Automated Lead Follow-up**: Never miss a lead again
- **24/7 Availability**: AI can call leads at optimal times
- **Consistent Quality**: Professional, standardized conversations
- **Increased Conversions**: Higher appointment booking rates

### For Operations
- **Scalable Outreach**: Handle hundreds of leads efficiently
- **Reduced Manual Work**: Automated calling and booking
- **Better Data**: Comprehensive call analytics and insights
- **Integration**: Seamless connection with existing CRM

## Next Steps

1. **VAPI Account Setup**: Create account at vapi.ai
2. **API Key Configuration**: Add your VAPI API key to environment
3. **Phone Number**: Purchase a VAPI phone number for outbound calls
4. **Testing**: Start with small test campaigns
5. **Optimization**: Refine assistant prompts based on results

## Support

For technical support or questions about the VAPI integration:
- Check the VAPI documentation: https://docs.vapi.ai
- Review call logs in the leads section
- Monitor webhook endpoints for debugging
- Contact support for advanced configuration needs

---

**Note**: This integration uses VAPI's mock service in development mode. For production use, ensure you have a valid VAPI API key and phone number configured.
